/**
 * 订单提取功能模块
 * 依赖于common-utils.js中的通用函数
 */

/**
 * 绑定提取订单modal的事件
 */
function bindExtractOrdersModalEvents() {
    // 快捷选择按钮点击事件
    $(document).on('click', '.quick-select-btn', function() {
        // 移除所有选中状态
        $('.quick-select-btn').removeClass('active');
        // 添加当前选中状态
        $(this).addClass('active');

        // 计算时间范围
        const days = parseInt($(this).data('days'));
        const now = new Date();
        
        // 结束时间为当前日期减去1天（因为获取不到今天的订单）
        const endDate = new Date(now);
        endDate.setDate(endDate.getDate() - 1);
        
        // 开始时间根据选择的天数计算
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - days + 1);

        // 设置表单值
        $('#orderStartDate').val(formatDateForInput(startDate));
        $('#orderEndDate').val(formatDateForInput(endDate));
        $('#orderStartTime').val('00:00');
        $('#orderEndTime').val('23:59');
    });

    // 确认提取按钮
    $('#confirmExtractOrders').on('click', function() {
        extractOrders();
    });
}

/**
 * 显示提取订单弹窗
 */
function showExtractOrdersModal() {
    // 设置默认时间（昨天）
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    
    $('#orderStartDate').val(formatDateForInput(yesterday));
    $('#orderEndDate').val(formatDateForInput(yesterday));
    $('#orderStartTime').val('00:00');
    $('#orderEndTime').val('23:59');

    // 加载主播列表
    loadAnchorsForOrders();

    // 显示弹窗
    $('#extractOrdersModal').removeClass('hidden');
}

/**
 * 隐藏提取订单弹窗
 */
function hideExtractOrdersModal() {
    $('#extractOrdersModal').addClass('hidden');
    // 重置表单
    $('#orderAnchorSelect').val('');
    $('.quick-select-btn').removeClass('active');
}

/**
 * 加载主播列表用于订单提取
 */
async function loadAnchorsForOrders() {
    try {
        const response = await fetch('/api/anchors?mode=full', {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取主播列表失败');
        }

        const anchorSelect = $('#orderAnchorSelect');
        anchorSelect.empty().append('<option value="">请选择主播</option>');

        if (data.anchors && data.anchors.length > 0) {
            // 按sort字段正序排列主播
            const sortedAnchors = [...data.anchors].sort((a, b) => {
                const sortA = parseInt(a.sort) || 0;
                const sortB = parseInt(b.sort) || 0;
                return sortA - sortB;
            });

            // 添加主播选项
            sortedAnchors.forEach(anchor => {
                anchorSelect.append(`<option value="${anchor.anchor_name}">${anchor.anchor_name}</option>`);
            });
        }
    } catch (error) {
        console.error('加载主播列表失败:', error);
        if (typeof layer !== 'undefined') {
            layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
        } else {
            alert('加载主播列表失败: ' + error.message);
        }
    }
}

/**
 * 提取订单数据
 */
async function extractOrders() {
    const anchorName = $('#orderAnchorSelect').val();
    const startDate = $('#orderStartDate').val();
    const endDate = $('#orderEndDate').val();
    const startTime = $('#orderStartTime').val();
    const endTime = $('#orderEndTime').val();

    if (!anchorName) {
        if (typeof layer !== 'undefined') {
            layer.msg('请选择主播', { icon: 2 });
        } else {
            alert('请选择主播');
        }
        return;
    }

    if (!startDate || !endDate) {
        if (typeof layer !== 'undefined') {
            layer.msg('请选择时间范围', { icon: 2 });
        } else {
            alert('请选择时间范围');
        }
        return;
    }

    // 构建时间字符串
    const beginTime = `${startDate} ${startTime}:00`;
    const endTimeStr = `${endDate} ${endTime}:59`;

    // 显示确认对话框
    const confirmMessage = `确定要提取主播"${anchorName}"在 ${beginTime} 到 ${endTimeStr} 期间的订单数据吗？`;
    
    let confirmed = false;
    if (typeof layer !== 'undefined') {
        layer.confirm(confirmMessage, {
            icon: 3,
            title: '确认提取订单'
        }, async function(index) {
            layer.close(index);
            await performOrderExtraction(anchorName, beginTime, endTimeStr);
        });
    } else {
        confirmed = confirm(confirmMessage);
        if (confirmed) {
            await performOrderExtraction(anchorName, beginTime, endTimeStr);
        }
    }
}

/**
 * 执行订单提取操作
 * @param {string} anchorName 主播名称
 * @param {string} beginTime 开始时间
 * @param {string} endTime 结束时间
 */
async function performOrderExtraction(anchorName, beginTime, endTime) {
    let loadingIndex = null;
    
    // 显示加载状态
    if (typeof layer !== 'undefined') {
        loadingIndex = layer.msg('正在提取订单数据...', {
            icon: 16,
            shade: 0.3,
            time: 0
        });
    } else {
        console.log('正在提取订单数据...');
    }

    try {
        const response = await fetch('/api/live-orders/extract', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...addApiKeyHeader()
            },
            body: JSON.stringify({
                anchorName: anchorName,
                beginTime: beginTime,
                endTime: endTime
            })
        });

        if (loadingIndex && typeof layer !== 'undefined') {
            layer.close(loadingIndex);
        }

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || '订单提取失败');
        }

        if (result.success) {
            const { total } = result.data;
            const message = `订单提取完成！共处理 ${total} 条订单`;
            
            if (typeof layer !== 'undefined') {
                layer.msg(message, {
                    icon: 1,
                    time: 5000
                });
            } else {
                alert(message);
            }

            // 隐藏弹窗
            hideExtractOrdersModal();
        } else {
            const errorMessage = '订单提取失败: ' + result.error;
            if (typeof layer !== 'undefined') {
                layer.msg(errorMessage, { icon: 2 });
            } else {
                alert(errorMessage);
            }
        }

    } catch (error) {
        if (loadingIndex && typeof layer !== 'undefined') {
            layer.close(loadingIndex);
        }
        console.error('提取订单失败:', error);
        const errorMessage = '提取订单失败: ' + error.message;
        if (typeof layer !== 'undefined') {
            layer.msg(errorMessage, { icon: 2 });
        } else {
            alert(errorMessage);
        }
    }
}

/**
 * 格式化日期为input[type=date]的格式
 * @param {Date} date 日期对象
 * @returns {string} YYYY-MM-DD格式的日期字符串
 */
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 导出函数供其他模块使用（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        bindExtractOrdersModalEvents,
        showExtractOrdersModal,
        hideExtractOrdersModal,
        loadAnchorsForOrders,
        extractOrders,
        performOrderExtraction,
        formatDateForInput
    };
} 