import fs from 'fs';

/**
 * 分析order.txt文件中的重复订单ID
 */
function analyzeOrderDuplicates() {
    try {
        // 读取文件内容
        const content = fs.readFileSync('order.txt', 'utf8');
        
        // 提取所有orderId
        const orderIdRegex = /"orderId":\s*"([^"]+)"/g;
        const orderIds = [];
        const orderIdMap = new Map();
        const duplicates = [];
        
        let match;
        let index = 0;
        
        while ((match = orderIdRegex.exec(content)) !== null) {
            const orderId = match[1];
            orderIds.push({
                orderId,
                position: index + 1
            });
            
            if (orderIdMap.has(orderId)) {
                // 发现重复
                const firstPosition = orderIdMap.get(orderId);
                duplicates.push({
                    orderId,
                    firstPosition,
                    duplicatePosition: index + 1
                });
            } else {
                orderIdMap.set(orderId, index + 1);
            }
            
            index++;
        }
        
        console.log('=== 网页数据重复订单ID分析 ===');
        console.log(`总订单数: ${orderIds.length}`);
        console.log(`唯一订单数: ${orderIdMap.size}`);
        console.log(`重复订单数: ${duplicates.length}`);
        
        if (duplicates.length > 0) {
            console.log('\n发现的重复订单ID:');
            duplicates.forEach((dup, index) => {
                console.log(`${index + 1}. 订单ID: ${dup.orderId}`);
                console.log(`   首次出现位置: ${dup.firstPosition}`);
                console.log(`   重复出现位置: ${dup.duplicatePosition}`);
                console.log(`   位置差: ${dup.duplicatePosition - dup.firstPosition}`);
                console.log('');
            });
            
            // 分析重复模式
            console.log('=== 重复模式分析 ===');
            const positionDiffs = duplicates.map(dup => dup.duplicatePosition - dup.firstPosition);
            const diffCounts = {};
            positionDiffs.forEach(diff => {
                diffCounts[diff] = (diffCounts[diff] || 0) + 1;
            });
            
            console.log('位置差统计:');
            Object.entries(diffCounts).sort((a, b) => parseInt(b[1]) - parseInt(a[1])).forEach(([diff, count]) => {
                console.log(`  相差${diff}位: ${count}次`);
            });
        } else {
            console.log('\n✅ 网页数据中没有发现重复的订单ID');
        }
        
        console.log('=== 分析完成 ===');
        
        return {
            totalOrders: orderIds.length,
            uniqueOrders: orderIdMap.size,
            duplicates: duplicates.length,
            duplicateDetails: duplicates
        };
        
    } catch (error) {
        console.error('分析失败:', error);
        return null;
    }
}

// 运行分析
analyzeOrderDuplicates();
