import crypto from 'crypto';
import { database } from './database.js';
import { handleTaobaoApiResponse } from './utils/response-handler.js';

/**
 * 直播计划同步工具
 */
class LivePlanSync {
    constructor() {
        this.appKey = '12574478';
        this.api = 'mtop.taobao.dreamweb.anchor.calendar.query';
        this.version = '1.0';
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 获取时间戳范围（15天前到三天后）
     * @returns {object}
     */
    getTimeRange() {
        const now = new Date();
        // 结束时间：三天后的23:59:59
        const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);
        const endTime = new Date(threeDaysLater.getFullYear(), threeDaysLater.getMonth(), threeDaysLater.getDate(), 23, 59, 59, 999).getTime();
        // 开始时间：15天前
        const startTime = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).getTime();

        return { startTime, endTime };
    }

    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 构建请求URL
     * @param {string} h5Token 
     * @param {object} data 
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();
        const sign = this.generateSign(h5Token, timestamp, data);
        
        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: this.api,
            v: this.version,
            preventFallback: 'true',
            type: 'json',
            dataType: 'json',
            data: JSON.stringify(data)
        });

        return {
            url: `https://h5api.m.taobao.com/h5/${this.api}/${this.version}/?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * 获取直播计划数据
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName - 主播名称，用于cookie更新
     * @param {string} roomNum
     * @returns {Promise<object>}
     */
    async fetchLivePlans(h5Token, fullCookie, anchorName, roomNum = '') {
        try {
            const { startTime, endTime } = this.getTimeRange();
            const data = {
                startTime,
                endTime,
                roomNum
            };

            const { url } = this.buildRequestUrl(h5Token, data);



            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Referer': 'https://liveplatform.taobao.com/restful/index/live/list',
                    'Cookie': fullCookie,
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'script',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-site'
                }
            });

            // 使用统一的响应处理
            const responseResult = await handleTaobaoApiResponse(response, anchorName, fullCookie);

            if (!responseResult.success) {
                return {
                    success: false,
                    error: responseResult.error,
                    errorType: responseResult.errorType,
                    cookieUpdated: responseResult.cookieUpdated,
                    data: []
                };
            }

            // 解析嵌套的数据结构
            let livePlans = [];
            if (responseResult.data && responseResult.data.aggregatedByDateLiveList) {
                responseResult.data.aggregatedByDateLiveList.forEach((dateGroup) => {
                    if (dateGroup && dateGroup.liveList && Array.isArray(dateGroup.liveList)) {
                        // 为每个直播添加日期信息
                        const livesWithDate = dateGroup.liveList.map(live => ({
                            ...live,
                            dateFromGroup: dateGroup.date
                        }));
                        livePlans = livePlans.concat(livesWithDate);
                    }
                });
            }

            return {
                success: true,
                data: livePlans,
                cookieUpdated: responseResult.cookieUpdated
            };

        } catch (error) {
            console.error('获取直播计划失败:', error);
            return {
                success: false,
                error: error.message,
                errorType: 'NETWORK_ERROR',
                data: []
            };
        }
    }

    /**
     * 解析直播状态
     * @param {string} roomStatus
     * @returns {string}
     */
    parseRoomStatus(roomStatus) {
        switch (String(roomStatus)) {
            case '0': return '未开播'; // 未开播
            case '1': return '直播中';      // 直播中
            case '2': return '已结束';     // 已结束
            default: return '未知';
        }
    }

    /**
     * 解析视频状态
     * @param {string} validStatus
     * @returns {string}
     */
    parseValidStatus(validStatus) {
        switch (String(validStatus)) {
            case '100': return '已展示'; // 已展示
            case '-9': return '已隐藏';  // 已隐藏
            default: return '已展示';    // 默认为已展示
        }
    }

    /**
     * 格式化时间
     * @param {number|string} timestamp
     * @returns {string}
     */
    formatTime(timestamp) {
        if (!timestamp) return null;
        try {
            // 确保时间戳是数字类型
            const numTimestamp = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
            if (isNaN(numTimestamp)) return null;

            const date = new Date(numTimestamp);
            if (isNaN(date.getTime())) return null;

            // 返回本地时间格式
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        } catch (error) {
            return null;
        }
    }

    /**
     * 格式化日期
     * @param {number} timestamp
     * @returns {string}
     */
    formatDate(timestamp) {
        if (!timestamp) return null;
        try {
            const date = new Date(timestamp);
            return date.toISOString().slice(0, 10);
        } catch (error) {
            return null;
        }
    }

    /**
     * 解析日期字符串 (YYYYMMDD格式)
     * @param {string} dateStr
     * @returns {string}
     */
    parseDateString(dateStr) {
        if (!dateStr || typeof dateStr !== 'string') return null;
        try {
            // 处理YYYYMMDD格式
            if (dateStr.length === 8) {
                const year = dateStr.substring(0, 4);
                const month = dateStr.substring(4, 6);
                const day = dateStr.substring(6, 8);
                return `${year}-${month}-${day}`;
            }
            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 转换直播计划数据格式
     * @param {array} rawData
     * @param {string} anchorName
     * @returns {array}
     */
    transformLivePlans(rawData, anchorName) {
        if (!Array.isArray(rawData)) {
            return [];
        }

        return rawData.map(item => {
            // 优先使用日期组的日期，然后尝试从时间戳解析
            let liveDate = null;
            if (item.dateFromGroup) {
                liveDate = this.parseDateString(item.dateFromGroup);
            }
            if (!liveDate) {
                liveDate = this.formatDate(item.appointmentTime || item.startTime);
            }
            // 如果还是没有日期，使用当前日期
            if (!liveDate) {
                liveDate = new Date().toISOString().slice(0, 10);
            }

            const scheduledTime = this.formatTime(item.appointmentTime);
            const startTime = this.formatTime(item.startTime);

            return {
                live_id: item.id || '',
                anchor_name: anchorName || item.userNick || '',
                live_title: item.title || '',
                live_category: Array.isArray(item.tags) ? item.tags.join(',') : (item.tags || ''),
                live_date: liveDate,
                scheduled_time: scheduledTime,
                start_time: startTime,
                live_status: this.parseRoomStatus(item.roomStatus),
                valid_status: this.parseValidStatus(item.validStatus), // 解析视频状态
                stream_url: item.liveUrl || '',
                product_count: 0, // 默认值，后续可以通过其他接口获取
                average_amount: 0,
                average_commission: 0,
                total_sales: 0,
                total_commission: 0
            };
        });
    }

    /**
     * 保存直播计划到数据库
     * @param {array} livePlans 
     * @returns {Promise<object>}
     */
    async saveLivePlans(livePlans) {
        if (!Array.isArray(livePlans) || livePlans.length === 0) {
            return { success: true, inserted: 0, updated: 0 };
        }

        let inserted = 0;
        let updated = 0;

        try {
            for (const plan of livePlans) {
                // 检查是否已存在
                const existing = await database.get(
                    'SELECT id FROM live_plans WHERE live_id = ?',
                    [plan.live_id]
                );

                if (existing) {
                    // 更新现有记录
                    await database.run(`
                        UPDATE live_plans SET
                            anchor_name = ?,
                            live_title = ?,
                            live_category = ?,
                            live_date = ?,
                            scheduled_time = ?,
                            start_time = ?,
                            live_status = ?,
                            valid_status = ?,
                            stream_url = ?,
                            updated_at = datetime('now', '+8 hours')
                        WHERE live_id = ?
                    `, [
                        plan.anchor_name,
                        plan.live_title,
                        plan.live_category,
                        plan.live_date,
                        plan.scheduled_time,
                        plan.start_time,
                        plan.live_status,
                        plan.valid_status,
                        plan.stream_url,
                        plan.live_id
                    ]);
                    updated++;
                } else {
                    // 插入新记录
                    await database.run(`
                        INSERT INTO live_plans (
                            live_id, anchor_name, live_title, live_category,
                            live_date, scheduled_time, start_time, live_status, valid_status,
                            stream_url, product_count, average_amount, average_commission,
                            total_sales, total_commission
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        plan.live_id,
                        plan.anchor_name,
                        plan.live_title,
                        plan.live_category,
                        plan.live_date,
                        plan.scheduled_time,
                        plan.start_time,
                        plan.live_status,
                        plan.valid_status,
                        plan.stream_url,
                        plan.product_count,
                        plan.average_amount,
                        plan.average_commission,
                        plan.total_sales,
                        plan.total_commission
                    ]);
                    inserted++;
                }
            }

            return { success: true, inserted, updated };

        } catch (error) {
            console.error('保存直播计划失败:', error);
            return { success: false, error: error.message, inserted, updated };
        }
    }

    /**
     * 删除多余的直播计划（根据日期对比）
     * @param {string} anchorName 主播名称
     * @param {array} newLivePlans 新获取的直播计划数据
     * @returns {Promise<object>}
     */
    async deleteExtraLivePlans(anchorName, newLivePlans) {
        let deleted = 0;

        try {
            // 获取同步时间范围
            const { startTime, endTime } = this.getTimeRange();

            // 将时间戳转换为日期字符串格式 (YYYY-MM-DD)
            const startDate = new Date(startTime).toISOString().split('T')[0];
            const endDate = new Date(endTime).toISOString().split('T')[0];

            // 从新数据中提取所有的live_id
            const newLiveIds = newLivePlans.map(plan => plan.live_id);

            // 查询数据库中该主播在时间范围内的所有直播计划
            const existingPlans = await database.all(`
                SELECT live_id, live_date
                FROM live_plans
                WHERE anchor_name = ?
                AND live_date >= ?
                AND live_date <= ?
            `, [anchorName, startDate, endDate]);

            if (existingPlans.results && existingPlans.results.length > 0) {
                // 找出数据库中存在但新数据中不存在的live_id
                const plansToDelete = existingPlans.results.filter(
                    existing => !newLiveIds.includes(existing.live_id)
                );

                // 删除多余的直播计划
                for (const planToDelete of plansToDelete) {
                    await database.run(
                        'DELETE FROM live_plans WHERE live_id = ?',
                        [planToDelete.live_id]
                    );
                    deleted++;
                    console.log(`删除多余的直播计划: ${planToDelete.live_id}, 日期: ${planToDelete.live_date}`);
                }
            }

            return { success: true, deleted };

        } catch (error) {
            console.error('删除多余直播计划失败:', error);
            return { success: false, error: error.message, deleted };
        }
    }

    /**
     * 同步指定主播的直播计划
     * @param {string} anchorName
     * @param {string} h5Token
     * @param {string} fullCookie
     * @returns {Promise<object>}
     */
    async syncAnchorLivePlans(anchorName, h5Token, fullCookie) {
        try {

            // 获取直播计划数据
            const result = await this.fetchLivePlans(h5Token, fullCookie, anchorName);

            if (!result.success) {
                return {
                    success: false,
                    error: result.error,
                    anchorName,
                    inserted: 0,
                    updated: 0,
                    deleted: 0
                };
            }

            // 转换数据格式
            const livePlans = this.transformLivePlans(result.data, anchorName);

            // 保存到数据库
            const saveResult = await this.saveLivePlans(livePlans);

            // 删除多余的直播计划（根据日期对比）
            const deleteResult = await this.deleteExtraLivePlans(anchorName, livePlans);

            return {
                success: saveResult.success && deleteResult.success,
                error: saveResult.error || deleteResult.error,
                anchorName,
                inserted: saveResult.inserted,
                updated: saveResult.updated,
                deleted: deleteResult.deleted,
                total: livePlans.length
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                anchorName,
                inserted: 0,
                updated: 0,
                deleted: 0
            };
        }
    }
}

export default LivePlanSync;
