import { handleTaobaoApiResponse } from './utils/response-handler.js';
import { database } from './database.js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

/**
 * 直播订单同步工具
 */
class LiveOrderSync {
    constructor() {
        this.appKey = '12574478';
        this.apiVersion = '1.0';
        this.baseUrl = 'https://h5api.m.taobao.com/h5/mtop.dreamweb.query.general.generalquery/1.0/';
        this.pageSize = 10; // 每页获取的数据量，可以根据需要调整
    }

    /**
     * 构建请求URL和参数
     * @param {string} h5Token
     * @param {object} data
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();
        
        // 构建参数字符串（用于签名计算）
        const paramStr = JSON.stringify({
            dataApi: "dataQRForm",
            param: JSON.stringify({
                dataQRFormId: "live_overview_order",
                queryUserRole: "ALL",
                beginTime: data.beginTime,
                endTime: data.endTime,
                orderDateType: "3",
                start: data.start.toString(), // 页数从0开始
                hit: data.hit.toString() // 每页数据量
            })
        });

        // 这里需要正确的签名算法，暂时使用示例中的签名
        const sign = this.generateSign(h5Token, timestamp, paramStr);

        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: 'mtop.dreamweb.query.general.generalQuery',
            v: this.apiVersion,
            dataType: 'json',
            preventFallback: 'true',
            type: 'json',
            data: paramStr
        });

        return {
            url: `${this.baseUrl}?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string).digest('hex');
    }

    /**
     * 生成签名
     * @param {string} h5Token
     * @param {number} timestamp
     * @param {string} data
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${data}`;
        return this.md5(signString);
    }

    /**
     * 获取订单总数
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @returns {Promise<object>}
     */
    async fetchOrderCount(h5Token, fullCookie, anchorName, beginTime, endTime) {
        try {
            const timestamp = Date.now();
            
            // 构建获取订单总数的参数
            const paramStr = JSON.stringify({
                dataApi: "live_trd_pay_ord_filter_stat",
                param: JSON.stringify({
                    beginTime,
                    endTime,
                    orderDateType: "3"
                })
            });

            const sign = this.generateSign(h5Token, timestamp, paramStr);

            const params = new URLSearchParams({
                jsv: '2.7.4',
                appKey: this.appKey,
                t: timestamp.toString(),
                sign: sign,
                api: 'mtop.dreamweb.query.general.generalQuery',
                v: this.apiVersion,
                dataType: 'json', // 改为json格式
                preventFallback: 'true',
                type: 'json', // 改为json格式
                data: paramStr
            });

            const url = `${this.baseUrl}?${params.toString()}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Referer': 'https://liveplatform.taobao.com/restful/index/data/transaction',
                    'Cookie': fullCookie,
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'script',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-site'
                }
            });

            // 使用统一的响应处理
            const responseResult = await handleTaobaoApiResponse(response, anchorName, fullCookie);
            if (!responseResult.success) {
                return {
                    success: false,
                    error: responseResult.error,
                    errorType: responseResult.errorType,
                    cookieUpdated: responseResult.cookieUpdated,
                    totalCount: 0
                };
            }



            // 解析订单总数
            let totalCount = 0;
            if (responseResult.data && responseResult.data.result && responseResult.data.result.length > 0) {
                const result = responseResult.data.result[0];
                totalCount = parseInt(result.pay_ord_cnt || '0');
            }

            console.log(`订单总数: ${totalCount}`);

            return {
                success: true,
                totalCount,
                cookieUpdated: responseResult.cookieUpdated
            };

        } catch (error) {
            console.error('获取订单总数失败:', error);
            return {
                success: false,
                error: error.message,
                totalCount: 0
            };
        }
    }

    /**
     * 获取单页订单数据
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @param {number} start - 偏移量，第一页=0，第二页=10，第三页=20...
     * @returns {Promise<object>}
     */
    async fetchOrderPage(h5Token, fullCookie, anchorName, beginTime, endTime, start = 0) {
        try {
            const data = {
                beginTime,
                endTime,
                start,
                hit: this.pageSize
            };

            const { url } = this.buildRequestUrl(h5Token, data);
        
            // 打印请求的data参数
            console.log(`请求参数 data:`, JSON.stringify(data, null, 2));

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Referer': 'https://liveplatform.taobao.com/restful/index/data/transaction',
                    'Cookie': fullCookie,
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'script',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-site'
                }
            });

            // 使用统一的响应处理
            const responseResult = await handleTaobaoApiResponse(response, anchorName, fullCookie);
            
            // 保存每次的结果到D:/{start}.json文件
            try {
                const filePath = `D:/${start}.json`;
                fs.writeFileSync(filePath, JSON.stringify(responseResult, null, 2), 'utf8');
                console.log(`已保存第${start}页结果到: ${filePath}`);
            } catch (saveError) {
                console.error(`保存文件失败 (${start}.json):`, saveError.message);
            }
            
            // 只打印第一次请求的响应内容
            if (start === 0 || start === 10) {
                console.log(`第${Math.floor(start / this.pageSize) + 1}页请求的响应结果:`, JSON.stringify(responseResult, null, 2));
            }
            
            if (!responseResult.success) {
                return {
                    success: false,
                    error: responseResult.error,
                    errorType: responseResult.errorType,
                    cookieUpdated: responseResult.cookieUpdated,
                    data: []
                };
            }



            // 解析订单数据
            let orders = [];
            if (responseResult.data && responseResult.data.result) {
                orders = responseResult.data.result;
            }

            const pageNumber = Math.floor(start / this.pageSize) + 1;
            console.log(`第 ${pageNumber} 页(start=${start})获取到 ${orders.length} 条订单数据`);
            
            // 打印每批响应的orderId
            if (orders.length > 0) {
                console.log(`第 ${pageNumber} 页订单ID列表:`);
                orders.forEach((order, index) => {
                    console.log(`  ${index + 1}. ${order.orderId || 'N/A'}`);
                });
            }

            return {
                success: true,
                data: orders,
                cookieUpdated: responseResult.cookieUpdated
            };

        } catch (error) {
            console.error('获取订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 获取所有订单数据（分页）
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @returns {Promise<object>}
     */
    async fetchOrders(h5Token, fullCookie, anchorName, beginTime, endTime) {
        try {
            // 首先获取订单总数
            console.log('正在获取订单总数...');
            const countResult = await this.fetchOrderCount(h5Token, fullCookie, anchorName, beginTime, endTime);
            
            if (!countResult.success) {
                return {
                    success: false,
                    error: countResult.error,
                    errorType: countResult.errorType,
                    cookieUpdated: countResult.cookieUpdated,
                    data: []
                };
            }

            const totalCount = countResult.totalCount;
            if (totalCount === 0) {
                console.log('没有订单数据');
                return {
                    success: true,
                    data: [],
                    cookieUpdated: countResult.cookieUpdated
                };
            }

            // 计算需要获取的页数
            const totalPages = Math.ceil(totalCount / this.pageSize);
            console.log(`订单总数: ${totalCount}，需要获取 ${totalPages} 页数据`);

            const allOrders = [];
            let lastCookieUpdate = countResult.cookieUpdated;

            // 按页数获取数据
            for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
                const startOffset = pageIndex * this.pageSize; // 计算偏移量
                console.log(`正在获取第 ${pageIndex + 1}/${totalPages} 页订单数据 (start=${startOffset})...`);
                
                const result = await this.fetchOrderPage(h5Token, fullCookie, anchorName, beginTime, endTime, startOffset);
                
                if (!result.success) {
                    console.error(`获取第 ${pageIndex + 1} 页数据失败:`, result.error);
                    // 如果某页失败，返回已获取的数据
                    return {
                        success: false,
                        error: result.error,
                        errorType: result.errorType,
                        cookieUpdated: result.cookieUpdated || lastCookieUpdate,
                        data: allOrders
                    };
                }

                // 记录cookie更新
                if (result.cookieUpdated) {
                    lastCookieUpdate = result.cookieUpdated;
                }

                // 添加获取到的数据
                if (result.data && result.data.length > 0) {
                    allOrders.push(...result.data);
                    console.log(`第 ${pageIndex + 1} 页获取到 ${result.data.length} 条数据，累计 ${allOrders.length} 条`);
                } else {
                    console.log(`第 ${pageIndex + 1} 页没有数据，提前结束`);
                    break;
                }

                // 添加延迟避免请求过于频繁
                if (pageIndex < totalPages - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            console.log(`订单数据获取完成，总共获取到 ${allOrders.length} 条订单数据`);

            return {
                success: true,
                data: allOrders,
                cookieUpdated: lastCookieUpdate
            };

        } catch (error) {
            console.error('获取订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 转换订单数据格式
     * @param {Array} rawOrders
     * @param {string} anchorName
     * @returns {Array}
     */
    transformOrders(rawOrders, anchorName) {
        return rawOrders.map(order => {
            return {
                anchor_name: anchorName,
                live_id: order.contentId || '',
                item_title: order.itemTitle || '',
                item_id: order.itemId || '',
                pay_time: order.payTime || null,
                pay_amount: parseFloat(order.divPayAmt || '0'),
                refund_amount: order.refundAmt && order.refundAmt !== '-' ? parseFloat(order.refundAmt) : 0,
                quantity: 1, // 订单数据中没有数量字段，默认为1
                parent_order_id: order.mordId || '',
                child_order_id: order.orderId || ''
            };
        });
    }

    /**
     * 保存订单数据到数据库
     * @param {Array} orders
     * @returns {Promise<object>}
     */
    async saveOrders(orders) {
        if (!orders || orders.length === 0) {
            return {
                success: true,
                inserted: 0,
                updated: 0,
                message: '没有订单数据需要保存'
            };
        }

        console.log(`开始保存 ${orders.length} 条订单数据...`);
        
        // 打印所有子订单ID
        console.log('所有子订单ID:');
        orders.forEach((order, index) => {
            console.log(`${index + 1}. ${order.child_order_id}`);
        });

        const db = database;

        let inserted = 0;
        let updated = 0;
        let skipped = 0;
        const errors = [];

        try {
            // 使用数据库的事务方法
            const result = await db.transaction(async () => {
                for (const order of orders) {
                    try {
                        // 先查询是否存在该订单
                        const existingOrder = await db.get(`
                            SELECT id, created_at FROM live_orders 
                            WHERE child_order_id = ?
                        `, [order.child_order_id]);

                        if (existingOrder) {
                            // 订单已存在，更新数据
                            const updateResult = await db.run(`
                                UPDATE live_orders SET
                                    anchor_name = ?,
                                    live_id = ?,
                                    item_title = ?,
                                    item_id = ?,
                                    pay_time = ?,
                                    pay_amount = ?,
                                    refund_amount = ?,
                                    quantity = ?,
                                    parent_order_id = ?,
                                    updated_at = datetime('now', '+8 hours')
                                WHERE child_order_id = ?
                            `, [
                                order.anchor_name,
                                order.live_id,
                                order.item_title,
                                order.item_id,
                                order.pay_time,
                                order.pay_amount,
                                order.refund_amount,
                                order.quantity,
                                order.parent_order_id,
                                order.child_order_id
                            ]);

                            if (updateResult.meta.changes > 0) {
                                updated++;
                            }
                        } else {
                            // 订单不存在，插入新数据
                            const insertResult = await db.run(`
                                INSERT INTO live_orders (
                                    anchor_name, live_id, item_title, item_id,
                                    pay_time, pay_amount, refund_amount, quantity,
                                    parent_order_id, child_order_id,
                                    created_at, updated_at
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                                    datetime('now', '+8 hours'), datetime('now', '+8 hours'))
                            `, [
                                order.anchor_name,
                                order.live_id,
                                order.item_title,
                                order.item_id,
                                order.pay_time,
                                order.pay_amount,
                                order.refund_amount,
                                order.quantity,
                                order.parent_order_id,
                                order.child_order_id
                            ]);

                            if (insertResult.meta.changes > 0) {
                                inserted++;
                            }
                        }
                        
                    } catch (error) {
                        console.error(`保存订单数据失败 ${order.child_order_id}:`, error);
                        errors.push(`订单 ${order.child_order_id}: ${error.message}`);
                    }
                }
                
                return { inserted, updated };
            });

            console.log(`订单保存完成: 新增 ${result.inserted} 条，更新 ${result.updated} 条，总计处理 ${orders.length} 条`);

            return {
                success: true,
                inserted: result.inserted,
                updated: result.updated,
                total: orders.length,
                errors: errors.length > 0 ? errors : null
            };

        } catch (error) {
            console.error('保存订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                inserted: 0,
                updated: 0
            };
        }
    }

    /**
     * 同步指定主播的订单数据
     * @param {string} anchorName
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} beginTime
     * @param {string} endTime
     * @returns {Promise<object>}
     */
    async syncAnchorOrders(anchorName, h5Token, fullCookie, beginTime, endTime) {
        try {
            console.log(`开始同步主播 ${anchorName} 的订单数据，时间范围: ${beginTime} - ${endTime}`);

            // 获取订单数据
            const result = await this.fetchOrders(h5Token, fullCookie, anchorName, beginTime, endTime);

            if (!result.success) {
                return {
                    success: false,
                    error: result.error,
                    anchorName,
                    inserted: 0,
                    updated: 0,
                    total: 0
                };
            }

            // 转换数据格式
            const orders = this.transformOrders(result.data, anchorName);

            // 保存到数据库
            const saveResult = await this.saveOrders(orders);

            return {
                success: saveResult.success,
                error: saveResult.error,
                anchorName,
                inserted: saveResult.inserted,
                updated: saveResult.updated,
                total: orders.length,
                cookieUpdated: result.cookieUpdated
            };

        } catch (error) {
            console.error('同步订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                anchorName,
                inserted: 0,
                updated: 0,
                total: 0
            };
        }
    }
}

export default LiveOrderSync; 