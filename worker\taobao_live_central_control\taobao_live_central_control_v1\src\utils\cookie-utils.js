/**
 * Cookie工具函数
 */

import { database } from '../database.js';

/**
 * 从Cookie中提取h5Token
 * @param {string} cookieString - Cookie字符串
 * @returns {string|null} - 提取的h5Token或null
 */
export function extractH5Token(cookieString) {
  if (!cookieString) return null;

  try {
    const cookies = cookieString.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === '_m_h5_tk' && value) {
        return value.split('_')[0];
      }
    }
  } catch (error) {
    console.error('Cookie解析失败:', error);
  }

  return "1";
}

/**
 * 验证h5Token并返回错误响应
 * @param {string} h5Token - 提取的token
 * @param {object} res - Express响应对象（可选）
 * @returns {boolean} - 是否有效
 */
export function validateH5Token(h5Token, res) {
  if (!h5Token) {
    if (res) {
      res.status(400).json({
        error: "Invalid token",
        message: "无法从主播Cookie中提取有效的h5_tk token"
      });
    }
    return false;
  }
  return true;
}

/**
 * 从Cookie中提取h5Token并验证
 * @param {string} cookieString - Cookie字符串
 * @param {object} res - Express响应对象（可选，为null时不发送响应）
 * @returns {string|null} - 有效的h5Token或null
 */
export function extractAndValidateH5Token(cookieString, res) {
  const h5Token = extractH5Token(cookieString);
  if (!validateH5Token(h5Token, res)) {
    return null;
  }
  return h5Token;
}

/**
 * 从响应头中解析set-cookie并提取新的cookie值
 * @param {Response} response - fetch响应对象
 * @returns {object} - 解析后的cookie信息
 */
/**
 * 智能分割cookie字符串，处理包含多个cookie的情况
 * @param {string} cookieLine - 包含多个cookie的字符串
 * @returns {Array<string>} - 分割后的各个cookie字符串
 */
function smartSplitCookies(cookieLine) {
  // 如果不包含逗号，直接返回
  if (!cookieLine.includes(',')) {
    return [cookieLine];
  }

  const cookies = [];
  let currentCookie = '';
  let i = 0;

  while (i < cookieLine.length) {
    const char = cookieLine[i];
    
    if (char === ',') {
      // 检查逗号后面是否跟着一个看起来像cookie名称的模式
      // 即: ", name=" 的格式
      let j = i + 1;
      
      // 跳过空格
      while (j < cookieLine.length && cookieLine[j] === ' ') {
        j++;
      }
      
      // 检查是否是新cookie的开始（包含等号的名称）
      let foundEquals = false;
      let tempIndex = j;
      while (tempIndex < cookieLine.length && cookieLine[tempIndex] !== ',' && cookieLine[tempIndex] !== ';') {
        if (cookieLine[tempIndex] === '=') {
          foundEquals = true;
          break;
        }
        tempIndex++;
      }
      
      if (foundEquals && j < cookieLine.length) {
        // 这是一个新cookie的开始
        cookies.push(currentCookie.trim());
        currentCookie = '';
        i = j; // 跳到新cookie的开始
        continue;
      }
    }
    
    currentCookie += char;
    i++;
  }

  // 添加最后一个cookie
  if (currentCookie.trim()) {
    cookies.push(currentCookie.trim());
  }

  return cookies;
}

export function parseSetCookieFromResponse(response) {
  // 获取所有set-cookie头部
  const setCookieHeaders = [];

  // 尝试多种方式获取set-cookie头部
  try {
    // 方法1: 尝试Node.js环境的headers.raw()方法
    if (response.headers.raw && typeof response.headers.raw === 'function') {
      const rawHeaders = response.headers.raw();
      if (rawHeaders['set-cookie']) {
        setCookieHeaders.push(...rawHeaders['set-cookie']);
      }
    }
    
    // 方法2: 尝试获取单个set-cookie头部（适用于浏览器环境）
    if (setCookieHeaders.length === 0) {
      const setCookie = response.headers.get('set-cookie') || response.headers.get('Set-Cookie');
      if (setCookie) {
        setCookieHeaders.push(setCookie);
      }
    }
    
    // 方法3: 遍历headers对象（适用于Node.js fetch）
    if (setCookieHeaders.length === 0 && response.headers.forEach) {
      response.headers.forEach((value, name) => {
        if (name.toLowerCase() === 'set-cookie') {
          setCookieHeaders.push(value);
        }
      });
    }
    
    // 方法4: 直接访问headers的entries（备用方法）
    if (setCookieHeaders.length === 0 && response.headers.entries) {
      for (const [name, value] of response.headers.entries()) {
        if (name.toLowerCase() === 'set-cookie') {
          setCookieHeaders.push(value);
        }
      }
    }
  } catch (error) {
    console.warn('获取set-cookie头部时发生错误:', error);
  }

  if (setCookieHeaders.length === 0) {
    return { hasNewCookies: false, cookies: {} };
  }

  console.log('检测到set-cookie响应头:', setCookieHeaders);

  const cookies = {};

  setCookieHeaders.forEach(cookieLine => {
    if (typeof cookieLine === 'string') {
      console.log(`🔍 解析cookie行: ${cookieLine}`);
      
      // 检查是否包含多个cookie（用逗号分隔）
      // 需要智能分割，避免分割cookie值中的逗号
      const individualCookies = smartSplitCookies(cookieLine);
      
      individualCookies.forEach(singleCookie => {
        const trimmedCookie = singleCookie.trim();
        if (trimmedCookie) {
          console.log(`🍪 处理单个cookie: ${trimmedCookie}`);
          
          // 解析cookie字符串，处理类似 "name=value;Path=/;Domain=taobao.com;..." 的格式
          const parts = trimmedCookie.split(';');
          if (parts.length > 0) {
            const cookiePart = parts[0].trim();
            const equalIndex = cookiePart.indexOf('=');
            if (equalIndex > 0) {
              const name = cookiePart.substring(0, equalIndex).trim();
              const value = cookiePart.substring(equalIndex + 1).trim();
              if (name && value) {
                cookies[name] = value;
                console.log(`✅ 解析到新cookie: ${name}=${value}`);
              }
            }
          }
        }
      });
    }
  });

  return {
    hasNewCookies: Object.keys(cookies).length > 0,
    cookies
  };
}

/**
 * 更新主播的cookie信息
 * @param {string} anchorName - 主播名称
 * @param {object} newCookies - 新的cookie键值对
 * @param {string} originalCookie - 原始cookie字符串
 * @returns {Promise<string>} - 更新后的cookie字符串
 */
export async function updateAnchorCookie(anchorName, newCookies, originalCookie) {
  try {
    console.log(`🍪 ===== 开始更新主播 ${anchorName} 的cookie =====`);
    console.log('📥 要添加/更新的新cookie:', JSON.stringify(newCookies, null, 2));
    console.log('📄 原始cookie长度:', originalCookie ? originalCookie.length : 0);
    console.log('📄 原始cookie前100字符:', originalCookie ? originalCookie.substring(0, 100) + '...' : 'null');

    // 解析原始cookie为Map，保持顺序
    const cookieMap = new Map();

    if (originalCookie && originalCookie.trim()) {
      // 按分号分割cookie
      const cookiePairs = originalCookie.split(';');

      for (const pair of cookiePairs) {
        const trimmedPair = pair.trim();
        if (trimmedPair) {
          const equalIndex = trimmedPair.indexOf('=');
          if (equalIndex > 0) {
            const name = trimmedPair.substring(0, equalIndex).trim();
            const value = trimmedPair.substring(equalIndex + 1).trim();
            if (name && value) {
              cookieMap.set(name, value);
              // 特别关注 _m_h5_tk 的值
              if (name === '_m_h5_tk') {
                console.log(`📌 原始 _m_h5_tk 值: ${value}`);
              }
            }
          }
        }
      }
    }

    console.log(`📊 原始cookie中包含 ${cookieMap.size} 个cookie项`);

    // 添加或更新新的cookie值
    let addedCount = 0;
    let updatedCount = 0;
    let actuallyUpdated = false;

    for (const [name, value] of Object.entries(newCookies)) {
      if (name && value) {
        if (cookieMap.has(name)) {
          const oldValue = cookieMap.get(name);
          if (oldValue !== value) {
            console.log(`🔄 更新cookie: ${name}=${value}`);
            console.log(`🔄 原值: ${name}=${oldValue}`);
            cookieMap.set(name, value);
            updatedCount++;
            actuallyUpdated = true;
            
            // 特别关注 _m_h5_tk 的更新
            if (name === '_m_h5_tk') {
              console.log(`🎯 重要！_m_h5_tk 已从 ${oldValue} 更新为 ${value}`);
            }
          } else {
            console.log(`⚪ cookie值未变: ${name}=${value}`);
          }
        } else {
          console.log(`➕ 添加新cookie: ${name}=${value}`);
          cookieMap.set(name, value);
          addedCount++;
          actuallyUpdated = true;
        }
      }
    }

    // 检查是否真的有更新
    if (!actuallyUpdated) {
      console.log(`⚠️ 没有实际的cookie需要更新，跳过数据库操作`);
      return originalCookie;
    }

    // 重新构建cookie字符串
    const cookieArray = [];
    for (const [name, value] of cookieMap) {
      cookieArray.push(`${name}=${value}`);
    }
    const newCookieString = cookieArray.join('; ');

    console.log(`📈 Cookie更新统计: 新增${addedCount}个, 更新${updatedCount}个, 总计${cookieMap.size}个`);
    console.log(`📏 原cookie长度: ${originalCookie ? originalCookie.length : 0}, 新cookie长度: ${newCookieString.length}`);
    
    // 检查新cookie中的 _m_h5_tk 值
    const newH5tk = cookieMap.get('_m_h5_tk');
    if (newH5tk) {
      console.log(`🔍 新cookie字符串中的 _m_h5_tk 值: ${newH5tk}`);
    }

    console.log(`💾 准备更新数据库 - 主播: ${anchorName}`);

    // 先检查主播是否存在
    const anchorCheck = await database.get(
      'SELECT id, anchor_name FROM anchors WHERE anchor_name = ?',
      [anchorName]
    );

    if (!anchorCheck) {
      console.error(`❌ 主播 ${anchorName} 在数据库中不存在！`);
      
      // 查找相似的主播名称
      const similarAnchors = await database.all(
        'SELECT anchor_name FROM anchors WHERE anchor_name LIKE ?',
        [`%${anchorName}%`]
      );
      
      if (similarAnchors.results && similarAnchors.results.length > 0) {
        console.log(`🔍 找到相似的主播名称:`, similarAnchors.results.map(a => a.anchor_name));
      } else {
        console.log(`🔍 没有找到相似的主播名称`);
      }
      
      return originalCookie;
    }

    console.log(`✅ 找到主播记录 - ID: ${anchorCheck.id}, 名称: ${anchorCheck.anchor_name}`);

    // 更新数据库（使用UTC+8时区）
    const updateResult = await database.run(
      'UPDATE anchors SET anchor_cookie = ?, updated_at = datetime(\'now\', \'+8 hours\') WHERE anchor_name = ?',
      [newCookieString, anchorName]
    );

    console.log(`💾 数据库更新结果:`, {
      success: updateResult.success,
      changes: updateResult.meta?.changes,
      lastRowId: updateResult.meta?.last_row_id
    });

    if (updateResult.success && updateResult.meta.changes > 0) {
      console.log(`✅ 主播 ${anchorName} 的cookie更新成功！`);
      
      // 验证数据库中的cookie是否正确更新
      const verifyResult = await database.get(
        'SELECT anchor_cookie FROM anchors WHERE anchor_name = ?',
        [anchorName]
      );
      
      if (verifyResult && verifyResult.anchor_cookie) {
        const verifyH5tk = extractH5Token(verifyResult.anchor_cookie);
        console.log(`🔬 验证：数据库中的 _m_h5_tk 值: ${verifyH5tk}`);
        
        if (verifyResult.anchor_cookie.includes('_m_h5_tk')) {
          const match = verifyResult.anchor_cookie.match(/_m_h5_tk=([^;]+)/);
          if (match) {
            console.log(`🔬 验证：数据库中完整的 _m_h5_tk: ${match[1]}`);
          }
        }
      }
      
      console.log(`🍪 ===== 主播 ${anchorName} cookie更新完成 =====`);
      return newCookieString;
    } else {
      console.error(`❌ 主播 ${anchorName} 的cookie更新失败: 数据库更新无变化`);
      console.error(`❌ 可能原因: 主播名称不存在或SQL执行失败`);
      return originalCookie;
    }
  } catch (error) {
    console.error(`💥 更新主播 ${anchorName} 的cookie失败:`, error);
    console.error(`💥 错误堆栈:`, error.stack);
    return originalCookie;
  }
}
